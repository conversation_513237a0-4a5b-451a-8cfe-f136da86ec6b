import { useState } from 'react';
import type {
  DoubleMaterialityAssessmentData,
  ESGCategory,
  MaterialPillar,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { chartTooltipOptions } from '@routes/summary/insights/utils/constants';
import { Chart } from 'react-google-charts';
import {
  chartStyles,
  chartConfigs,
  DEFAULT_CHART_COLORS,
  LegendType,
  MATERIALITY_CHART_COLORS,
  renderDoubleMaterialityTooltip,
  CHART_TICKS,
} from './chart-utils';
import { BasicAlert } from '@g17eco/molecules/alert';
import { isScoreInRange, NOT_FOUND_CATEGORY_MESSAGE } from './utils';
import type { ReactGoogleChartEvent } from 'react-google-charts/dist/types';

interface Props {
  data: DoubleMaterialityAssessmentData[];
  category: LegendType;
  selectedTopicCode: string;
  clickHandler: (index: number) => void;
}

const WIDTH = '100%';
const HEIGHT = 640;

const options = {
  chartArea: {
    ...chartStyles.chartArea,
    left: 50,
    right: 20,
    height: HEIGHT,
  },
  hAxis: {
    title: 'IMPACT',
    ...chartStyles.axis,
    textPosition: 'out',
    ticks: CHART_TICKS,
    gridlines: {
      color: 'transparent',
    },
  },
  vAxis: {
    title: 'FINANCIAL',
    ...chartStyles.axis,
    textPosition: 'out',
    ticks: CHART_TICKS,
    gridlines: {
      color: 'transparent',
    },
  },
  legend: chartStyles.legend,
  tooltip: chartTooltipOptions,
};

const getDotStyle = ({
  currentIndex,
  hoveredIndex,
  selectedIndex,
}: {
  currentIndex: number;
  hoveredIndex: number | null;
  selectedIndex: number;
}) => {
  switch (currentIndex) {
    case selectedIndex:
      return 'point { opacity: 1; size: 10; }';
    case hoveredIndex:
      return 'point { opacity: 0.33; size: 10; }';
    default:
      return 'point { opacity: 0.33; size: 6; }';
  }
};

export const ScatterChart = ({ data, category, selectedTopicCode, clickHandler }: Props) => {
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const config = chartConfigs.find((config) => config.code === category);
  const selectedIndex = data.findIndex((topic) => topic.code === selectedTopicCode);

  if (!config) {
    return <BasicAlert type='warning'>{NOT_FOUND_CATEGORY_MESSAGE}</BasicAlert>;
  }

  const chartEvents: ReactGoogleChartEvent[] = [
    {
      eventName: 'ready',
      callback: ({ chartWrapper, google }) => {
        const chart = chartWrapper.getChart();
        /**
         * setSelection is exposed by the library, but it doesn't exist in the types.d.ts
         * https://github.com/rakannimer/react-google-charts/issues/33
         */
        // @ts-ignore
        chart.setSelection([]);

        /**
         * Following the example from https://github.com/rakannimer/react-google-charts/issues/263
         * */
        google.visualization.events.addListener(chart as any, 'onmouseover' as any, (event: any) => {
          // The event object for onmouseover has a `row` property
          const { row } = event;
          setHoveredIndex(row);
        });

        google.visualization.events.addListener(chart as any, 'onmouseout' as any, (_event) => {
          setHoveredIndex(null);
        });
      },
    },
    {
      eventName: 'select',
      callback({ chartWrapper }) {
        const chart = chartWrapper.getChart();
        const [selection] = chart.getSelection();
        if (!selection) {
          return;
        }
        clickHandler(selection.row);
        // @ts-ignore
        chart.setSelection([]);
      },
    },
  ];

  const chartData = [
    [
      'Impact',
      ...Object.values(config.categories)
        .map(({ label }) => [label, { role: 'style' }, { type: 'string', role: 'tooltip', p: { html: true } }])
        .flat(),
    ],
    ...data.map((topic, topicIndex) => [
      topic.nonFinancialRelativeScore ?? 0,
      ...Object.keys(config.categories)
        .map((code) => {
          let financialRelativeScore = NaN;
          switch (category) {
            case LegendType.ESG:
              if (topic.categories?.esg?.includes(code as ESGCategory)) {
                financialRelativeScore = topic.financialRelativeScore ?? NaN;
              }
              break;
            case LegendType.MaterialPillar:
              if (topic.categories?.materialPillar?.includes(code as MaterialPillar)) {
                financialRelativeScore = topic.financialRelativeScore ?? NaN;
              }
              break;
            case LegendType.Materiality: {
              const { min, max } = config.categories[code];
              if (isScoreInRange({ score: topic.normalizedScore, min, max })) {
                financialRelativeScore = topic.financialRelativeScore ?? NaN;
              }
              break;
            }
          }

          return [
            financialRelativeScore,
            getDotStyle({ currentIndex: topicIndex, hoveredIndex, selectedIndex }),
            renderDoubleMaterialityTooltip(topic),
          ];
        })
        .flat(),
    ]),
  ];

  return (
    <Chart
      chartType='ScatterChart'
      chartEvents={chartEvents}
      width={WIDTH}
      height={HEIGHT}
      data={chartData}
      options={{
        ...options,
        colors: category === LegendType.Materiality ? MATERIALITY_CHART_COLORS : DEFAULT_CHART_COLORS,
      }}
    />
  );
};
